using UnityEngine;
using UnityEditor;
using BoatControllerwithShooting;

[CustomEditor(typeof(BoatController))]
public class BoatControllerEditor : Editor
{
    private SerializedProperty moveForce;
    private SerializedProperty rotateTorque;
    private SerializedProperty MaxSpeed;
    private SerializedProperty speedBoostTag;
    private SerializedProperty slowSpeed;
    private SerializedProperty normalMaxSpeed;
    private SerializedProperty onWater;
    private SerializedProperty rotationSpeed;

    private void OnEnable()
    {
        moveForce = serializedObject.FindProperty("moveForce");
        rotateTorque = serializedObject.FindProperty("rotateTorque");
        MaxSpeed = serializedObject.FindProperty("MaxSpeed");
        speedBoostTag = serializedObject.FindProperty("speedBoostTag");
        slowSpeed = serializedObject.FindProperty("slowSpeed");
        normalMaxSpeed = serializedObject.FindProperty("normalMaxSpeed");
        onWater = serializedObject.FindProperty("onWater");
        rotationSpeed = serializedObject.FindProperty("rotationSpeed");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        // Header with custom styling
        GUIStyle headerStyle = new GUIStyle(GUI.skin.label);
        headerStyle.fontSize = 16;
        headerStyle.fontStyle = FontStyle.Bold;
        headerStyle.normal.textColor = Color.cyan;
        headerStyle.alignment = TextAnchor.MiddleCenter;

        GUILayout.Space(10);
        GUILayout.Label("🚤 BOAT CONTROLLER", headerStyle);
        
        GUIStyle brandingStyle = new GUIStyle(GUI.skin.label);
        brandingStyle.fontSize = 10;
        brandingStyle.fontStyle = FontStyle.Italic;
        brandingStyle.normal.textColor = Color.yellow;
        brandingStyle.alignment = TextAnchor.MiddleCenter;
        
        GUILayout.Label("Boat Controller Develop by Ali Taj", brandingStyle);
        GUILayout.Space(10);

        // Movement Settings
        EditorGUILayout.LabelField("Movement Settings", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(moveForce, new GUIContent("Move Force", "Force applied for forward/backward movement"));
        EditorGUILayout.PropertyField(rotateTorque, new GUIContent("Rotate Torque", "Torque applied for turning"));
        EditorGUILayout.PropertyField(rotationSpeed, new GUIContent("Rotation Speed", "Speed of boat rotation stabilization"));
        
        GUILayout.Space(10);

        // Speed Control Settings
        EditorGUILayout.LabelField("Speed Control Settings", EditorStyles.boldLabel);
        
        // Create a colored box for speed settings
        GUI.backgroundColor = Color.green;
        EditorGUILayout.BeginVertical("box");
        GUI.backgroundColor = Color.white;
        
        EditorGUILayout.PropertyField(speedBoostTag, new GUIContent("Speed Boost Tag", "Tag that allows normal speed (default: Water)"));
        EditorGUILayout.PropertyField(normalMaxSpeed, new GUIContent("Normal Max Speed", "Maximum speed when on speed boost tag"));
        EditorGUILayout.PropertyField(slowSpeed, new GUIContent("Slow Speed", "Speed when NOT on speed boost tag (km/h)"));
        EditorGUILayout.PropertyField(MaxSpeed, new GUIContent("Current Max Speed", "Current maximum speed (runtime)"));
        
        EditorGUILayout.EndVertical();
        
        GUILayout.Space(10);

        // Status Information
        EditorGUILayout.LabelField("Status Information", EditorStyles.boldLabel);
        
        GUI.backgroundColor = onWater.boolValue ? Color.cyan : Color.red;
        EditorGUILayout.BeginVertical("box");
        GUI.backgroundColor = Color.white;
        
        EditorGUILayout.PropertyField(onWater, new GUIContent("On Water", "Is the boat currently on water?"));
        
        if (Application.isPlaying)
        {
            BoatController boat = (BoatController)target;
            EditorGUILayout.LabelField("Current Speed", $"{boat.CurrentSpeed:F1} km/h");
            EditorGUILayout.LabelField("Health", boat.Health.ToString());
        }
        
        EditorGUILayout.EndVertical();
        
        GUILayout.Space(10);

        // Help Box
        EditorGUILayout.HelpBox(
            "Speed Control System:\n" +
            "• Set 'Speed Boost Tag' to the tag that allows normal speed\n" +
            "• When boat is ON the tagged surface: Normal Max Speed\n" +
            "• When boat is OFF the tagged surface: Slow Speed (5 km/h)\n" +
            "• Default tag is 'Water' for water-based speed boost",
            MessageType.Info
        );

        // Draw the rest of the default inspector
        GUILayout.Space(10);
        EditorGUILayout.LabelField("Other Settings", EditorStyles.boldLabel);
        
        // Draw all other properties
        DrawPropertiesExcluding(serializedObject, 
            "moveForce", "rotateTorque", "MaxSpeed", "speedBoostTag", 
            "slowSpeed", "normalMaxSpeed", "onWater", "rotationSpeed");

        serializedObject.ApplyModifiedProperties();
    }
}
